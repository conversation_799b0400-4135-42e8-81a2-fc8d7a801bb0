@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --sidebar-width: 220px;
    --sidebar-collapsed-width: 70px;
    --p-datatable-body-cell-padding: 0.75rem 0.75rem;
    --p-datatable-body-cell-border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.dark {
    --p-datatable-row-striped-background: #212121;
    --table-odd-bg-color: #252525;
    --border-color: rgba(255, 255, 255, 0.05);
    --sidebar-border-color: rgba(255, 255, 255, 0.1);
    --table-container-border-color: rgba(255, 255, 255, 0.125);
    --bg-color: #171717;
    --table-header-bg: #212121;
    --content-bg-color: #212121;
    --sidebar-hover-bg-color: #333;
    --scroll-color: #424242;
    --input-bg: #2a2a2a;
    --input-border: #3a3a3a; // #404040
    --input-color: #e0e0e0;
    --input-placeholder: #888888; // #666666
    --input-focus: #4f46e5; // #10b981
    --gray-600: #676767;
    --gray-700: #424242;
}

html, body {
    @apply bg-gray-100/50 text-gray-900 select-none h-svh dark:bg-[var(--bg-color)] dark:text-gray-300;
}

@layer utilities {
    .transition-ease-200 {
        @apply transition ease-in-out duration-200;
    }

    .bg-primary {
        @apply transition-ease-200 bg-sky-500 hover:bg-sky-600 border-sky-500 dark:bg-sky-700 dark:border-sky-700/90 dark:hover:bg-sky-800;
    }

    .text-primary {
        @apply text-gray-100 dark:text-gray-50;
    }

    .theme-color {
        @apply transition-ease-200 bg-gray-50 dark:bg-[var(--bg-color)] dark:text-gray-200;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@layer components {
    .app-header {
        @apply flex h-16 shrink-0 items-center gap-2 pr-4 transition-[width,height] ease-linear;
    }

    .sidebar {
        &-wrapper {
            @apply flex min-h-svh w-full;
        }

        &-bg {
            @apply flex h-full w-full flex-col;
        }

        &-header {
            @apply h-16 mr-[1px] gap-2 p-4 flex items-center;

            &-logo {
                @apply flex aspect-square size-8 items-center justify-center rounded-md;
            }

            &-app-name {
                @apply ml-1 grid text-left;
            }
        }

        &-content {
            @apply flex min-h-0 flex-1 flex-col gap-2;
        }

        &-menu {
            @apply flex w-full min-w-0 flex-col gap-1;

            &-btn {
                @apply flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left transition-all ease-in-out duration-150 hover:bg-gray-200 border border-transparent hover:border-gray-300;
                @apply dark:hover:bg-neutral-800/80 dark:hover:border-[var(--sidebar-border-color)];

                >span:last-child {
                    @apply truncate;
                }

                >svg {
                    @apply size-5 shrink-0 text-gray-600 dark:text-gray-400;
                }

                &[data-active=true] {
                    @apply border bg-gray-200 border-gray-400 hover:bg-gray-300 dark:border-[var(--sidebar-border-color)] dark:hover:bg-neutral-800 dark:bg-neutral-800/80;
                }

                &.app-logo {
                    @apply border-0 hover:bg-transparent hover:text-sky-600 dark:hover:text-gray-400;
                }
            }
        }

        &-group {
            @apply relative flex w-full min-w-0 flex-col p-2;
        }

        &-footer {
            @apply flex flex-col gap-2 p-2;

            .user-dropdown {
                &-btn {
                    @apply border border-gray-300 dark:border-[var(--sidebar-border-color)] rounded shadow hover:bg-gray-200 dark:hover:bg-neutral-800/80 hover:border-gray-400;

                    &[data-state=open] {
                        @apply bg-gray-200 border-gray-400 dark:border-[var(--sidebar-border-color)] dark:bg-neutral-800/80;
                    }
                }
            }

            .user {
                &-avatar {
                    @apply relative flex size-8 shrink-0 overflow-hidden rounded-full bg-sky-500 dark:bg-sky-700;
                }

                &-info {
                    @apply grid flex-1 text-left text-sm leading-tight;
                }
            }
        }

        &-primary {
            @apply h-svh flex-col flex transition-[width] duration-200 ease-linear;

            &[data-state=expanded] {
                @apply w-[var(--sidebar-width)];

                .sidebar-menu-btn {
                    span {
                        @apply transition ease-in-out duration-300;
                    }
                }
            }

            &[data-state=collapsed] {
                @apply w-[var(--sidebar-collapsed-width)];

                .sidebar-header {
                    .sidebar-menu-btn {
                        @apply justify-center;

                        span {
                            @apply hidden;
                        }

                        &.app-logo {
                            @apply p-0;
                        }
                    }

                    .sidebar-header-app-name {
                        @apply hidden;
                    }
                }

                .sidebar-footer {
                    .user-info,
                    .dropdown-icon {
                        @apply hidden;
                    }

                    .user-dropdown-btn {
                        @apply border-0 bg-none shadow-none p-0 flex justify-center cursor-default;
                        &:hover {
                            background-color: transparent !important;
                        }

                        &[data-state=open] {
                            @apply border-0;
                            background-color: transparent !important;
                        }

                        .user-avatar {
                            @apply hover:bg-sky-500 dark:hover:bg-sky-800 cursor-pointer;
                        }
                    }
                }
            }
        }
    }

    .dropdown-menu {
        &-separator {
            @apply bg-gray-200 dark:bg-[var(--sidebar-border-color)] -mx-1 my-1 h-px;
        }

        &-item {
            @apply relative flex items-center cursor-default gap-2 rounded-sm px-2 py-1.5 text-sm outline-none select-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4;

            &[data-disabled] {
                @apply pointer-events-none;
            }
        }

        &-link {
            @apply w-full flex items-center transition-[background] hover:bg-gray-200 dark:hover:bg-neutral-700/50 hover:cursor-pointer;
        }
    }

    .user-dropdown {
        &-content {
            @apply w-[220px] min-w-56 rounded-lg bg-white dark:bg-[var(--bg-color)] dark:border-[var(--sidebar-border-color)];
            animation-duration: 0.5s;
            animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
            animation-name: slideUp;

            .user {
                &-avatar {
                    @apply bg-sky-400 dark:bg-sky-800;
                }

                &-info {
                    @apply flex flex-col;

                    &-wrapper {
                        @apply flex items-center gap-2 px-1 py-1.5 text-left text-sm;
                    }
                }
            }
        }
    }

    .dialog {
        &-title {
            @apply text-lg leading-none font-semibold;
        }

        &-header {
            @apply flex flex-col gap-2 text-center sm:text-left;
        }

        &-content {
            --width: 512px;
            left: calc((100% - var(--width)) / 2);
            transform: translateY(20px);
            @apply w-[var(--width)] bg-gray-200 dark:border-zinc-700 dark:bg-zinc-900 top-[calc(50%-250px)] fixed z-50 grid gap-4 rounded-lg border p-6 shadow-md dark:shadow-lg dark:shadow-black/50 duration-200;
        }

        &-description {

        }

        &-footer {
            @apply flex flex-col-reverse gap-2 sm:flex-row sm:justify-end;
        }
    }

    .main-wrapper {
        @apply transition-[margin] ease-in-out duration-300 relative w-full;

        .app-header {
            @apply transition-all ease-in-out duration-300;
        }

        &-content {
            @apply h-[calc(100vh-64px)] bg-gray-200 border-gray-300 dark:bg-[var(--content-bg-color)] dark:border-[var(--border-color)] overflow-y-auto rounded-tl-lg border shadow-sm p-6 space-y-6;
        }
    }

    .grid-content-wrapper {
        @apply space-y-5;
    }

    .input {
        &-field {
            @apply w-full py-2 px-3 border border-gray-300 dark:bg-neutral-600/50 rounded-md shadow-sm placeholder-gray-400 font-light transition;
            @apply focus:outline-none focus:border-sky-400 focus:ring focus:ring-sky-200 disabled:bg-gray-200;
            @apply dark:border-[var(--input-border)] dark:bg-zinc-900/70 dark:text-[var(--input-color)] dark:placeholder-[var(--input-placeholder)];
            @apply dark:focus:border-gray-600 dark:focus:ring-gray-600/50;
        }

        &-checkbox {
            @apply size-4 shrink-0 rounded-[4px] border border-gray-400 transition ease-in-out duration-150 outline-none disabled:cursor-not-allowed disabled:opacity-50;

            &[data-state=checked] {
                @apply bg-sky-500 border-sky-600 dark:border-sky-500 text-white;
            }
        }

        &-label {
            @apply flex items-center leading-none font-normal select-none;

            &[data-disabled=true] {
                @apply pointer-events-none opacity-50 cursor-not-allowed;
            }
        }
    }

    .btn {
        &-base {
            @apply flex items-center justify-center px-4 py-2 border border-transparent rounded-md;
            @apply transition ease-in-out duration-150 focus:outline-none hover:cursor-pointer;

            &.setting-item {
                @apply transition ease-linear duration-150 justify-start text-gray-900 dark:text-gray-300;

                &:hover,
                &.active {
                    @apply border border-gray-400 bg-neutral-500/20 dark:border-[var(--border-color)] dark:bg-neutral-700/30 text-gray-900 dark:text-gray-300;
                }
            }
        }

        &-primary {
            @apply bg-primary text-primary;
        }

        &-ghost {
            @apply hover:text-gray-600 dark:hover:text-gray-400;
        }

        &-destructive {
            @apply text-white bg-red-400 border-red-400 hover:bg-red-600 dark:bg-red-800 dark:border-red-800/50 dark:hover:bg-red-900;
        }

        &-secondary {
            @apply bg-white dark:bg-neutral-800/50 hover:bg-gray-100 dark:hover:bg-neutral-900 border border-gray-300 dark:border-[var(--border-color)] text-gray-700 dark:text-gray-500 dark:hover:text-gray-400;
        }
    }

    .status-container {
        @apply mb-4 text-center text-sm font-semibold text-green-600;
    }

    .tooltip {
        &-content {
            @apply bg-sky-400 dark:bg-sky-700 text-primary z-50 w-fit rounded-md px-3 py-1.5 text-balance;
            transform-origin: var(--reka-tooltip-content-transform-origin);
            animation: scaleIn 0.15s ease-out;
        }

        &-arrow {
            @apply hidden bg-sky-400 dark:bg-sky-700 z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px];

            > path {
                @apply hidden;
            }
        }
    }

    .card {
        &-container {
            @apply theme-color;
            @apply flex flex-col gap-6 rounded-xl border border-gray-200 dark:border-gray-500/30 py-6 shadow-sm overflow-hidden;
        }
    }
}

.v-toast {
    &__item {
        max-width: 350px;
    }

    &__text {
        font-size: 16px !important;
        padding: 10px !important;
    }

    &__icon {
        margin-left: 10px !important;
    }
}

.p-datatable {
    @apply border-0 rounded-lg overflow-hidden;

    &-table {
        @apply border-0 rounded-lg overflow-hidden;

        &-container {
            @apply w-full;
        }

        tr {
            @apply flex;
        }

        th {
            @apply font-semibold;
        }

        th, td {
            @apply px-3 py-3 border-0 truncate select-text;

            &.flex-1 {
                @apply min-w-48;
            }
        }

        td {
            &[data-pc-section="emptymessagecell"] {
                flex: 1;
            }
        }

        thead tr {
            @apply dark:bg-[var(--table-header-bg)] shadow dark:drop-shadow-[0_0_1px_rgba(255,255,255,0.2)];
        }

        tbody {
            tr {
                @apply border-b-0 border-t dark:border-[var(--border-color)] odd:bg-neutral-300/30 even:bg-white dark:odd:bg-[var(--table-odd-bg-color)];

                td {
                    @apply border-0;
                }
            }
        }

        &-container {
            @apply border border-gray-300/90 dark:border-[var(--table-container-border-color)] rounded-lg shadow-sm dark:shadow-none;
        }
    }
}

.action-column {
    .p-datatable-column-header-content {
        @apply w-full;
        display: flex;
        justify-content: space-between !important;
    }
}

.number-column {
    width: 100px;
}

.grid-form-filter {
    input {
        font-weight: 300;
    }
}

.search-clear-icon {
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

.pagination {
    &-item {
        @apply mb-1 mr-1 px-3.5 py-2.5 text-sm leading-4 transition border-gray-200 dark:border-[var(--border-color)] dark:bg-[var(--table-odd-bg-color)];
    }
}

.dark {
    .main-wrapper-content {
        scrollbar-color: var(--gray-700) transparent;

        &:hover {
            scrollbar-color: var(--gray-600) transparent;
        }
    }
}

.grid-data-table {
    .flex-1 {
        @apply min-w-48;
    }

    &.grid-loading {
        @apply relative;

        &::before {
            content: '';
            @apply absolute inset-0 opacity-10 z-10;
        }

        .p-datatable-table {
            @apply pointer-events-none opacity-50;
        }
    }
}
