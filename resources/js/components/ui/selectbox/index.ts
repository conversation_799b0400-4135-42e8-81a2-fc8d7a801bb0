import { cva, type VariantProps } from 'class-variance-authority'

export interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}

export { default as SelectBox } from './SelectBox.vue'

export const selectBoxVariants = cva(
  'relative w-full cursor-default rounded-md border border-gray-200 px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'border-gray-200',
        error: 'border-destructive focus:ring-destructive',
      },
      size: {
        default: 'h-10',
        sm: 'h-9 px-2 text-xs',
        lg: 'h-11 px-4',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
)

export type SelectBoxVariants = VariantProps<typeof selectBoxVariants>
