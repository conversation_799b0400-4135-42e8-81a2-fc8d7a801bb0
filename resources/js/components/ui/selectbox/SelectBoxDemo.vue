<script setup lang="ts">
import { ref } from 'vue'
import { type SelectBoxOption, SelectBox } from '.'

// Demo data
const selectedValue = ref<string | number>()
const selectedValue2 = ref<string | number>()
const refreshLoading = ref(false)

const options = ref<SelectBoxOption[]>([
  { value: '1', label: 'Option 1' },
  { value: '2', label: 'Option 2' },
  { value: '3', label: 'Option 3' },
  { value: '4', label: 'Option 4 (Disabled)', disabled: true },
  { value: '5', label: 'Option 5' },
])

const countries = ref<SelectBoxOption[]>([
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'de', label: 'Germany' },
  { value: 'fr', label: 'France' },
  { value: 'jp', label: 'Japan' },
  { value: 'au', label: 'Australia' },
])

const handleRefresh = () => {
  refreshLoading.value = true
  
  // Simulate API call
  setTimeout(() => {
    // Add a new option to demonstrate refresh
    const newOption = {
      value: `new-${Date.now()}`,
      label: `New Option ${options.value.length + 1}`
    }
    options.value.push(newOption)
    refreshLoading.value = false
  }, 1500)
}

const handleClear = () => {
  console.log('Clear button clicked')
}
</script>

<template>
  <div class="p-8 space-y-8 max-w-md mx-auto">
    <h1 class="text-2xl font-bold text-center mb-8">SelectBox Demo</h1>
    
    <!-- Basic SelectBox -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">Basic SelectBox</h2>
      <SelectBox
        v-model="selectedValue"
        label="Choose an option"
        :options="options"
        placeholder="Select your option..."
        @clear="handleClear"
      />
      <p class="text-sm text-muted-foreground">
        Selected: {{ selectedValue || 'None' }}
      </p>
    </div>

    <!-- SelectBox with Refresh -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">SelectBox with Refresh</h2>
      <SelectBox
        v-model="selectedValue2"
        label="Options with refresh"
        :options="options"
        placeholder="Select and try refresh..."
        show-refresh
        :refresh-loading="refreshLoading"
        @refresh="handleRefresh"
        @clear="handleClear"
      />
      <p class="text-sm text-muted-foreground">
        Selected: {{ selectedValue2 || 'None' }}
      </p>
    </div>

    <!-- SelectBox without Label -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">SelectBox without Label</h2>
      <SelectBox
        :options="countries"
        placeholder="Choose country..."
        :show-label="false"
      />
    </div>

    <!-- SelectBox without Clear -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">SelectBox without Clear</h2>
      <SelectBox
        label="No clear button"
        :options="countries"
        placeholder="Choose country..."
        :show-clear="false"
      />
    </div>

    <!-- Small SelectBox -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">Small SelectBox</h2>
      <SelectBox
        label="Small size"
        :options="countries"
        placeholder="Choose country..."
        size="sm"
      />
    </div>

    <!-- Large SelectBox -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">Large SelectBox</h2>
      <SelectBox
        label="Large size"
        :options="countries"
        placeholder="Choose country..."
        size="lg"
      />
    </div>

    <!-- Error State -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">Error State</h2>
      <SelectBox
        label="With error"
        :options="countries"
        placeholder="Choose country..."
        error
      />
    </div>

    <!-- Disabled State -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">Disabled State</h2>
      <SelectBox
        label="Disabled"
        :options="countries"
        placeholder="Choose country..."
        disabled
      />
    </div>
  </div>
</template>
