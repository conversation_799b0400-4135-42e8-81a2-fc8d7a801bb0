<script setup lang="ts">
import type { ActionButton, BaseFormFilter, UserPaginatorData } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import AppLayout from '@/layouts/app/AppLayout.vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { SearchInput } from '@/components/ui/input/index';
import { computed, ref } from 'vue';
import PaginationPage from '@/components/PaginationPage.vue';

const { t } = useI18n();

const right: ActionButton = {
    label: t('user.create'),
    size: 'sm',
    action: () => {
        console.log('create user');
    },
};

interface Props {
    filters?: BaseFormFilter
    paginator?: UserPaginatorData,
}

const props = withDefaults(defineProps<Props>(), {
    filters: () => ({
        keyword: '',
    }),
})

const form = useForm({
    keyword: props.filters['keyword'] ?? '',
});

const search = () => {
    if (form.processing) {
        return false;
    }

    form.get(route('dashboard'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
};

const clearSearch = () => {
    form.keyword = '';
    search();
}

const gridLoading = ref<boolean>(false);
const busy = computed(() => gridLoading.value || form.processing);

const updateProgress = (progress: number | null) => {
    gridLoading.value = progress !== null;
};

const title = t('user.list');
</script>

<template>
    <Head :title="title" />

    <AppLayout :title="title" :right="right">
        <PaginationPage
            :busy="busy"
            :show-pagination="props.paginator && props.paginator.data.length > 0"
            :links="props.paginator?.links ?? []"
            @updateProgress="updateProgress"
        >
            <template #filter>
                <SearchInput
                    v-model="form.keyword"
                    :placeholder="t('form.search')"
                    :on-search="search"
                    :on-clear-search="clearSearch"
                    :disabled="busy"
                />
            </template>

            <template #datatable>
                <DataTable :value="props.paginator?.data" stripedRows>
                    <Column class="number-column" field="user_id" :header="t('ID')" />
                    <Column class="flex-1" field="name" :header="t('user.name')" />
                    <Column class="w-[320px]" field="email" :header="t('email.label')" />
                    <Column class="action-column w-[100px]">

                    </Column>
                    <template #empty>{{ t(form.keyword.length ? 'emptyResult' : 'emptyData') }}</template>
                </DataTable>
            </template>
        </PaginationPage>
    </AppLayout>
</template>
