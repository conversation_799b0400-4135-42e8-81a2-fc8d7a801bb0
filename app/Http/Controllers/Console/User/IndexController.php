<?php

namespace App\Http\Controllers\Console\User;

use App\Data\User\FetchUserDataDTO;
use App\Http\Controllers\Controller;
use App\Http\Resources\Console\UserResource;
use App\Services\UserService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class IndexController extends Controller
{
    /**
     * @param UserService $service
     */
    public function __construct(protected UserService $service)
    {

    }

    /**
     * @param Request $request
     * @return \Inertia\Response
     */
    public function __invoke(Request $request)
    {
        $payload = $request->only([
            'limit',
            'page',
            'keyword',
        ]);

        $dto = FetchUserDataDTO::fromArray($payload);
        $dto->setOrders([
            'user_id' => 'desc',
        ]);

        $users = $this->service->pagination($dto);
        $users->getCollection()->transform(fn ($item) => UserResource::make($item));

        return Inertia::render('user/Index', [
            'paginator' => $users->onEachSide(2),
            'filters' => $payload,
        ]);
    }
}
