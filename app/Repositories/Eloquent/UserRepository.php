<?php

namespace App\Repositories\Eloquent;

use App\Data\FetchDataDTO;
use App\Data\User\FetchUserDataDTO;
use App\Models\User;
use App\Repositories\Contracts\FilterableRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class UserRepository extends BaseRepository implements UserRepositoryInterface, FilterableRepositoryInterface
{
    /**
     * @return Builder
     */
    protected function query(): Builder
    {
        return User::query();
    }

    /**
     * @param string $email
     * @return null|User
     */
    public function findByEmail(string $email): ?User
    {
        return $this->query()->where('email', $email)->first();
    }

    /**
     * @param Model $model
     * @param array $payload
     * @return Model
     */
    public function update(Model $model, array $payload)
    {
        $model->fill($payload);
        if ($model->isDirty('email')) {
            $model->fill([
                'email_verified_at' => null,
            ]);
        }

        $model->save();

        return $model;
    }

    /**
     * @param Builder $query
     * @param FetchUserDataDTO $dto
     * @return void
     */
    public function applyQueryFilter(Builder $query, FetchDataDTO $dto): void
    {
        $query->when($dto->getKeyword(), fn ($q, $keyword) => $q->search($keyword));
    }
}
